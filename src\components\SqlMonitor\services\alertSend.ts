import { httpClient } from '@/services/http/client';
import type { AlertSend, AlertSendSearchParams, AlertSendSubmitData, ApiResponse } from '../types';
import type { KafkaReceiver, PrometheusReceiver } from '@/types/task';

export class AlertSendService {
  // ==================== 告警发送 CRUD 方法 ====================

  /**
   * 添加告警发送配置
   * @param data 告警发送配置数据
   * @returns 新创建的告警发送配置信息
   */
  static async addAlertSend(data: AlertSend): Promise<ApiResponse<AlertSendSubmitData>> {
    console.log('添加告警发送配置:', data);

    const requestData: AlertSendSubmitData = {
      ...data,
      config: JSON.stringify(data.config),
    };

    const res = httpClient.post<ApiResponse<AlertSendSubmitData>>('/api/v1/alert/send/add', requestData);

    console.log(requestData);

    return res;
  }

  /**
   * 删除单个告警发送配置
   * @param id 告警发送配置ID
   * @returns 删除结果
   */
  static async deleteAlertSend(id: number): Promise<ApiResponse<AlertSend>> {
    console.log('删除告警发送配置:', id);

    const params = {
      ids: [id],
    };

    const res = httpClient.post<ApiResponse<AlertSend>>('/api/v1/alert/send/delete/id', params);

    return res;
  }

  /**
   * 批量删除告警发送配置
   * @param ids 告警发送配置ID数组
   * @returns 删除结果
   */
  static async batchDeleteAlertSends(ids: number[]): Promise<ApiResponse<AlertSend>> {
    console.log('批量删除告警发送配置:', ids);

    const params = {
      ids: ids,
    };

    const res = httpClient.post<ApiResponse<AlertSend>>('/api/v1/alert/send/delete/id', params);

    return res;
  }

  /**
   * 更新告警发送配置
   * @param data 更新数据
   * @returns 更新后的告警发送配置信息
   */
  static async updateAlertSend(data: AlertSend): Promise<ApiResponse<AlertSendSubmitData>> {
    console.log('更新告警发送配置:', data);

    // 前端config为对象，后端要求为json字符串，所以需要转换
    const requestData: AlertSendSubmitData = {
      ...data,
      config: JSON.stringify(data.config),
    };

    const res = httpClient.post<ApiResponse<AlertSendSubmitData>>('/api/v1/alert/send/update', requestData);

    console.log(res);

    return res;
  }

  /**
   * 获取告警发送列表
   * @returns 告警发送列表
   */
  static async getAlertSends(params: Partial<AlertSendSearchParams>): Promise<ApiResponse<AlertSend>> {
    // 调用API获取数据
    const res = await httpClient.post<ApiResponse<AlertSendSubmitData>>('/api/v1/alert/send/select', params);
    console.log('API响应数据:', res);

    // 将后端数据结构转换成前端数据结构
    const responseData: ApiResponse<AlertSend> = {
      ...res,
      data: res.data.map(item => {
        let config = null;

        if (item.receive_type == 'kafka') {
          config = JSON.parse(item.config) as KafkaReceiver;
        } else {
          config = JSON.parse(item.config) as PrometheusReceiver;
        }

        return {
          ...item,
          config: config,
        };
      }),
    };

    console.log('转换后的响应数据:', responseData);

    return responseData;
  }

  static async getAlertSendsByIds(ids: string[]): Promise<ApiResponse<AlertSend>> {
    const params = {
      ids: ids,
    };
    // 调用API获取数据
    const res = await httpClient.post<ApiResponse<AlertSendSubmitData>>('/api/v1/alert/send/select/ids', params);
    console.log('API响应数据:', res);

    // 将后端数据结构转换成前端数据结构
    const responseData: ApiResponse<AlertSend> = {
      ...res,
      data: res.data.map(item => {
        let config = null;

        if (item.receive_type == 'kafka') {
          config = JSON.parse(item.config) as KafkaReceiver;
        } else {
          config = JSON.parse(item.config) as PrometheusReceiver;
        }

        return {
          ...item,
          config: config,
        };
      }),
    };

    console.log('转换后的响应数据:', responseData);

    return responseData;
  }
}

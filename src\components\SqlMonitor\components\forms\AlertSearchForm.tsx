import React from 'react';
import { Form, Input, Select, Button, Row, Col } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import type { FormInstance } from 'antd';

import type { TaskAlertSearchParams } from '../../types';
import { tableStyles } from '../../styles';

const { Option } = Select;

interface AlertQuickSearchFormProps {
  form: FormInstance;
  onSubmit: (values: TaskAlertSearchParams) => void;
  onReset: () => void;
}

/**
 * 告警快速搜索表单组件
 */
export const AlertQuickSearchForm: React.FC<AlertQuickSearchFormProps> = ({ form, onSubmit, onReset }) => {
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSubmit(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <div className={tableStyles.searchSection}>
      <Form form={form} onFinish={onSubmit} className="w-full">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="name" className="mb-0">
                <Input
                  placeholder="请输入告警名称"
                  prefix={<SearchOutlined className="text-gray-400" />}
                  allowClear
                  className="rounded-md"
                  autoComplete="off"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck={false}
                />
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="severity" className="mb-0">
                <Select placeholder="请选择告警级别" allowClear className="w-full rounded-md">
                  <Option value="low">低</Option>
                  <Option value="medium">中</Option>
                  <Option value="high">高</Option>
                  <Option value="critical">严重</Option>
                </Select>
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="type" className="mb-0">
                <Select placeholder="请选择告警类型" allowClear className="w-full rounded-md">
                  <Option value="isExist">存在性判断</Option>
                  <Option value="isValue">查询值判断</Option>
                  <Option value="isChange">查询值变化判断</Option>
                </Select>
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={24} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <div className="flex gap-2 items-center h-full">
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />} className={tableStyles.buttonPrimary}>
                  搜索
                </Button>
                <Button onClick={onReset} className="rounded-md flex-1" icon={<ReloadOutlined />}>
                  重置
                </Button>
              </div>
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

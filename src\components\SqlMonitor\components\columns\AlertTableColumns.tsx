/**
 * 告警表格列配置组件
 * 用于定义告警管理表格的列结构、筛选器和操作按钮
 */

import { DeleteOutlined, EditOutlined, SearchOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { Button, Input, Popconfirm, Space } from 'antd';
import type { FilterDropdownProps, FilterValue } from 'antd/es/table/interface';
import React from 'react';

import type { TaskAlert } from '@/components/SqlMonitor/types';
import { tableStyles } from '@/components/SqlMonitor/styles';

/**
 * 告警表格列配置组件的属性接口
 */
interface AlertTableColumnsProps {
  /** 表格筛选状态信息 */
  filteredInfo: Record<string, FilterValue | null>;
  /** 获取指定列的排序状态 */
  getSortOrder: (columnKey: string) => 'ascend' | 'descend' | null;
  /** 编辑告警的回调函数 */
  onEdit: (record: TaskAlert) => void;
  /** 删除告警的回调函数 */
  onDelete: (id: number) => void;
}

/**
 * 获取列搜索筛选器配置
 * 为指定列生成搜索筛选下拉框的配置对象
 */
const getColumnSearchProps = (dataIndex: string, placeholder: string, filteredInfo: Record<string, FilterValue | null>) => ({
  // 自定义筛选下拉框组件
  filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: FilterDropdownProps) => (
    <div style={{ padding: 8 }}>
      {/* 搜索输入框 */}
      <Input
        placeholder={`搜索 ${placeholder}`}
        value={selectedKeys[0]}
        onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
        onPressEnter={() => confirm()}
        style={{ marginBottom: 8, display: 'block' }}
      />
      {/* 操作按钮组 */}
      <Space>
        {/* 搜索按钮 */}
        <Button type="primary" onClick={() => confirm()} icon={<SearchOutlined />} size="small" style={{ width: 90 }}>
          搜索
        </Button>
        {/* 重置按钮 */}
        <Button
          onClick={() => {
            if (clearFilters) {
              clearFilters();
            }
            confirm();
          }}
          size="small"
          style={{ width: 90 }}
        >
          重置
        </Button>
      </Space>
    </div>
  ),
  // 筛选图标，根据是否有筛选条件显示不同颜色
  filterIcon: (filtered: boolean) => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
  // 筛选逻辑：模糊匹配（不区分大小写）
  onFilter: (value: React.Key | boolean, record: TaskAlert) => record[dataIndex as keyof TaskAlert]?.toString().toLowerCase().includes(value.toString().toLowerCase()),
  // 当前筛选值
  filteredValue: filteredInfo[dataIndex] || null,
});

/**
 * 获取列选择筛选器配置
 * 为指定列生成下拉选择筛选器的配置对象
 */
const getColumnSelectProps = (dataIndex: string, options: { text: string; value: string }[], filteredInfo: Record<string, FilterValue | null>) => ({
  // 筛选选项
  filters: options,
  // 筛选逻辑
  onFilter: (value: React.Key | boolean, record: TaskAlert) => {
    const fieldValue = record[dataIndex as keyof TaskAlert];
    return fieldValue?.toString().includes(value.toString());
  },
  // 当前筛选值
  filteredValue: filteredInfo[dataIndex] || null,
});

/**
 * 创建告警表格列配置
 * 根据传入的参数生成完整的表格列配置数组
 */
export const createAlertTableColumns = ({ filteredInfo, getSortOrder, onEdit, onDelete }: AlertTableColumnsProps): TableColumnsType<TaskAlert> => [
  // 告警名称列
  {
    title: '告警名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    ellipsis: true,
    fixed: 'left',
    sorter: (a, b) => a.name.localeCompare(b.name),
    sortOrder: getSortOrder('name'),
    ...getColumnSearchProps('name', '告警名称', filteredInfo),
  },
  // 告警级别列
  {
    title: '告警级别',
    dataIndex: 'severity',
    key: 'severity',
    width: 120,
    sorter: (a, b) => a.severity.localeCompare(b.severity),
    sortOrder: getSortOrder('severity'),
    // ...getColumnSelectProps(
    //   'severity',
    //   [
    //     { text: '低', value: 'low' },
    //     { text: '中', value: 'medium' },
    //     { text: '高', value: 'high' },
    //     { text: '严重', value: 'critical' },
    //   ],
    //   filteredInfo
    // ),
    // render: (severity: string) => {
    //   const colorMap: Record<string, string> = {
    //     low: 'blue',
    //     medium: 'orange',
    //     high: 'red',
    //     critical: 'purple',
    //   };
    //   const labelMap: Record<string, string> = {
    //     low: '低',
    //     medium: '中',
    //     high: '高',
    //     critical: '严重',
    //   };
    //   return <Tag color={colorMap[severity] || 'default'}>{labelMap[severity] || severity}</Tag>;
    // },
  },
  // 告警类型列
  {
    title: '告警类型',
    dataIndex: 'alert_type',
    key: 'alert_type',
    width: 150,
    sorter: (a, b) => a.alert_type.localeCompare(b.alert_type),
    sortOrder: getSortOrder('type'),
    ...getColumnSelectProps(
      'type',
      [
        { text: '存在性判断', value: 'isExist' },
        { text: '查询值判断', value: 'isValue' },
        { text: '查询值变化判断', value: 'isChange' },
      ],
      filteredInfo
    ),
    render: (type: string) => {
      const labelMap: Record<string, string> = {
        isExist: '存在性判断',
        isValue: '查询值判断',
        isChange: '查询值变化判断',
      };
      return labelMap[type] || type;
    },
  },
  // SQL语句列
  {
    title: 'SQL语句',
    dataIndex: 'sql',
    key: 'sql',
    width: 300,
    ellipsis: true,
    ...getColumnSearchProps('sql', 'SQL语句', filteredInfo),
    render: (sql: string) => <div style={{ maxWidth: 280, overflow: 'hidden', textOverflow: 'ellipsis' }}>{sql}</div>,
  },
  // 触发值列
  {
    title: '触发值',
    dataIndex: 'values',
    key: 'values',
    width: 150,
    // render: (values: string[]) => {
    //   if (!values || values.length === 0) {
    //     return '-';
    //   }
    //   return values.join(',');
    // },
  },
  // 操作列（编辑和删除按钮）
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    filteredValue: null,
    render: (_, record) => (
      <Space size="small">
        {/* 编辑按钮 */}
        <Button type="text" size="small" icon={<EditOutlined />} onClick={() => onEdit(record)} className={tableStyles.editButton}>
          编辑
        </Button>
        {/* 删除按钮（带确认弹窗） */}
        <Popconfirm title="确认删除" description="确定要删除这个告警配置吗？" onConfirm={() => onDelete(record.id)} okText="确定" cancelText="取消" placement="topRight">
          <Button type="text" size="small" danger icon={<DeleteOutlined />} className={tableStyles.deleteButton}>
            删除
          </Button>
        </Popconfirm>
      </Space>
    ),
  },
];

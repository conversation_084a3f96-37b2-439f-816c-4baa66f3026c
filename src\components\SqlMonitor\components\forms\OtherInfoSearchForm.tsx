import React from 'react';
import { Form, Input, But<PERSON>, Row, Col } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import type { FormInstance } from 'antd';

import type { OtherInfoSearchParams } from '../../types';
import { tableStyles } from '../../styles';

interface OtherInfoQuickSearchFormProps {
  form: FormInstance;
  onSubmit: (values: OtherInfoSearchParams) => void;
  onReset: () => void;
}

/**
 * 其他信息快速搜索表单组件
 * 提供其他信息的快速搜索功能
 */
export const OtherInfoQuickSearchForm: React.FC<OtherInfoQuickSearchFormProps> = ({ form, onSubmit, onReset }) => {
  const handleSubmit = (values: any) => {
    // 过滤空值
    const filteredValues = Object.keys(values).reduce((acc, key) => {
      if (values[key] !== undefined && values[key] !== null && values[key] !== '') {
        acc[key] = values[key];
      }
      return acc;
    }, {} as any);

    console.log('其他信息搜索参数:', filteredValues);
    onSubmit(filteredValues);
  };

  const handleReset = () => {
    form.resetFields();
    onReset();
  };

  return (
    <div className={tableStyles.searchSection}>
      <Form form={form} onFinish={handleSubmit} className="w-full">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="name" className="mb-0">
                <Input
                  placeholder="请输入信息名称"
                  prefix={<SearchOutlined className="text-gray-400" />}
                  allowClear
                  className="rounded-md"
                  autoComplete="off"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck={false}
                />
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="business" className="mb-0">
                <Input
                  placeholder="请输入业务系统"
                  prefix={<SearchOutlined className="text-gray-400" />}
                  allowClear
                  className="rounded-md"
                  autoComplete="off"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck={false}
                />
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="hostname" className="mb-0">
                <Input
                  placeholder="请输入主机名称"
                  prefix={<SearchOutlined className="text-gray-400" />}
                  allowClear
                  className="rounded-md"
                  autoComplete="off"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck={false}
                />
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={24} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <div className="flex gap-2 items-center h-full">
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />} className={tableStyles.buttonPrimary}>
                  搜索
                </Button>
                <Button onClick={handleReset} className="rounded-md flex-1" icon={<ReloadOutlined />}>
                  重置
                </Button>
              </div>
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

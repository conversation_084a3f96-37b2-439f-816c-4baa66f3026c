import { ExclamationCircleOutlined, EditOutlined } from '@ant-design/icons';
import { Form, Modal, App, Drawer } from 'antd';
import React, { useCallback, useState, useRef, useEffect } from 'react';
import type { AxiosError } from 'axios';

// 导入重构后的模块
import type { ApiResponse, TaskAlert, TaskAlertSearchParams } from '../types';
import { TaskService } from '../services';
import { useAlertData, useDrawer } from '../hooks';
import { useAlertTable } from '../hooks/useAlertTable';
import { useSelection } from '../hooks/useSelection';
import { tableStyles } from '../styles';

// 导入拆分的组件
import { createAlertTableColumns } from '@/components/SqlMonitor/components/columns';
import { AlertQuickSearchForm } from '@/components/SqlMonitor/components/forms';
import { AlertActionButtons } from '@/components/SqlMonitor/components/button';
import { AlertTableComponent } from '@/components/SqlMonitor/components/table';
import {AlertBasicForm} from '@/components/SqlMonitor/components/forms';

interface AlertTableProps {
  contentHeight?: number;
}

/**
 * 告警管理表格组件
 * 包含查询、新增、编辑、删除、分页等完整功能
 */
const AlertTable: React.FC<AlertTableProps> = ({ contentHeight }) => {
  const { message } = App.useApp();

  // 使用自定义hooks管理状态
  const { data, loading, total, pagination, loadData, refreshData, resetData, updateSearchParams, updatePagination } = useAlertData({ autoLoad: true });

  const { allSelectedRows, rowSelection, clearSelection, getSelectedCount } = useSelection(data, {
    crossPage: true,
    onSelectionChange: (selectedKeys: React.Key[], selectedRows: TaskAlert[]) => {
      console.log('选择变化:', { selectedKeys, selectedRows });
    },
  });

  const { tableScrollY, filteredInfo, handleTableChange, getSortOrder, resetSortAndFilter } = useAlertTable({ contentHeight });

  const { drawerState: editDrawer, showDrawer: showEditDrawer, hideDrawer: hideEditDrawer } = useDrawer();

  // 当前编辑记录
  const [currentRecord, setCurrentRecord] = useState<TaskAlert | null>(null);
  const [submitLoading, setSubmitLoading] = useState(false);

  // 表单实例
  const [searchForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // 使用 ref 来避免依赖循环
  const loadDataRef = useRef(loadData);

  useEffect(() => {
    loadDataRef.current = loadData;
  }, [loadData]);

  // 搜索表单提交处理
  const handleSearchFormSubmit = useCallback(
    (values: TaskAlertSearchParams) => {
      console.log('搜索表单数据:', values);

      // 更新搜索参数状态
      updateSearchParams(values);

      // 重置排序和筛选状态
      resetSortAndFilter();

      // 重置选择状态
      clearSelection();

      // 使用搜索参数加载数据
      loadDataRef.current({
        ...values,
        current: 1, // 重置到第一页
      });
    },
    [updateSearchParams, resetSortAndFilter, clearSelection]
  );

  // 重置搜索
  const handleReset = useCallback(() => {
    resetData();

    // 重置所有状态
    searchForm.resetFields();
    resetSortAndFilter();
    clearSelection();

    // 重新加载数据，明确传递空的搜索参数以确保重置生效
    // loadDataRef.current({
    //   current: 1,
    //   page_size: DEFAULT_PAGINATION.page_size,
    //   // 明确传递空的搜索参数，覆盖可能还未更新的状态
    //   name: undefined,
    //   severity: undefined,
    //   type: undefined,
    //   sql: undefined,
    // });
  }, [resetData, resetSortAndFilter, clearSelection, searchForm]);

  // 删除单个告警
  const handleDelete = useCallback(
    async (id: number) => {
      try {
        const res = await TaskService.deleteAlert(id);
        message.success(`成功删除告警数量 ${res.total}`);
        refreshData();
      } catch (error) {
        message.error('删除失败');
        console.error('删除失败:', error);
      }
    },
    [refreshData, message]
  );

  // 批量删除告警
  const handleBatchDelete = useCallback(async () => {
    const totalSelected = getSelectedCount();
    if (totalSelected === 0) {
      message.warning('请先选择要删除的告警');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${totalSelected} 个告警吗？`,
      icon: <ExclamationCircleOutlined />,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const ids = Array.from(allSelectedRows.keys()).map(key => Number(key));
          const res = await TaskService.batchDeleteAlerts(ids);
          message.success(`成功删除告警数量 ${res.total}`);
          clearSelection();
          refreshData();
        } catch (error) {
          message.error('批量删除失败');
          console.error('批量删除失败:', error);
        }
      },
    });
  }, [getSelectedCount, allSelectedRows, message, clearSelection, refreshData]);

  // 抽屉关闭处理
  const handleDrawerClose = useCallback(() => {
    // 先清空当前记录，这样 useEffect 就不会重新设置表单值
    setCurrentRecord(null);
    // 然后重置表单数据
    editForm.resetFields();
    // 最后隐藏抽屉
    hideEditDrawer();
  }, [hideEditDrawer, editForm]);

  // 编辑告警处理
  const handleEdit = useCallback(
    (record: TaskAlert) => {
      setCurrentRecord(record);
      showEditDrawer({
        title: '编辑告警',
        width: '60%',
      });
    },
    [showEditDrawer]
  );

  // 表单提交处理
  const handleFormSubmit = useCallback(
    async (formVal: TaskAlert) => {
      setSubmitLoading(true);
      try {
        console.log('提交表单数据:', formVal);

        if (currentRecord) {
          const res = await TaskService.updateAlert(formVal);
          if (res.success) {
            message.success('更新成功');
          } else {
            message.error('更新失败');
            return;
          }
        } else {
          const res = await TaskService.addAlert(formVal);
          if (res.success) {
            message.success('保存成功');
          } else {
            message.error('保存失败,' + res.message);
            return;
          }
        }

        // 关闭抽屉并刷新数据
        hideEditDrawer();
        setCurrentRecord(null);
        refreshData();
      } catch (error: unknown) {
        const axiosError = error as AxiosError<ApiResponse<TaskAlert>>;
        const errorData = axiosError.response?.data;
        const errorMessage = errorData?.message || axiosError.message || '保存失败';
        message.error(errorMessage);
        console.error('错误详情:', errorData || axiosError);
      } finally {
        setSubmitLoading(false);
      }
    },
    [hideEditDrawer, refreshData, message, currentRecord]
  );

  // 表格列定义
  const columns = createAlertTableColumns({
    filteredInfo,
    getSortOrder,
    onEdit: handleEdit,
    onDelete: handleDelete,
  });

  return (
    <div className="h-full flex flex-col">
      {/* 主要内容区域  */}
      <div className={tableStyles.mainContainer}>
        {/* 快速搜索表单区域 */}
        <AlertQuickSearchForm form={searchForm} onSubmit={handleSearchFormSubmit} onReset={handleReset} />

        {/* 操作按钮区域 */}
        <AlertActionButtons
          selectedCount={getSelectedCount()}
          onAddAlert={() => {
            // 先清空当前记录
            setCurrentRecord(null);
            // 显示抽屉（此时 useEffect 会因为 initialData 为 null 而重置表单）
            showEditDrawer({
              title: '新增告警',
              width: '60%',
            });
          }}
          onBatchDelete={handleBatchDelete}
          onClearSelection={clearSelection}
        />

        {/* 表格主体区域 */}
        <AlertTableComponent
          columns={columns}
          data={data}
          loading={loading}
          total={total}
          pagination={pagination}
          rowSelection={rowSelection}
          tableScrollY={tableScrollY}
          onTableChange={handleTableChange}
          onPaginationChange={(page: number, pageSize: number) => {
            updatePagination(page, pageSize);
            loadDataRef.current({
              current: page,
              page_size: pageSize,
            });
          }}
        />
      </div>

      {/* 编辑抽屉 */}
      <Drawer
        title={
          <div className="flex items-center gap-2">
            <EditOutlined className="text-blue-600" />
            <span className="text-lg font-semibold">{currentRecord ? '编辑告警' : '新增告警'}</span>
          </div>
        }
        width={editDrawer.width}
        open={editDrawer.visible}
        onClose={handleDrawerClose}
        maskClosable={false}
        className="custom-drawer"
        footer={null}
      >
        <AlertBasicForm
          form={editForm}
          initialData={currentRecord || undefined}
          onSubmit={handleFormSubmit}
          onCancel={handleDrawerClose}
          onReset={() => {
            editForm.resetFields();
            message.info('表单已重置');
          }}
          loading={submitLoading}
        />
      </Drawer>
    </div>
  );
};

export default AlertTable;

import type { DBConnection, AlertSend, OtherInfo } from '../types/task';

/**
 * 生成随机字符串
 */
function generateRandomString(length: number): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成随机IP地址
 */
function generateRandomIP(): string {
  return `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
}

/**
 * 生成随机端口号
 */
function generateRandomPort(): string {
  return (Math.floor(Math.random() * 65535) + 1024).toString();
}

/**
 * 生成随机时间戳
 */
function generateRandomTimestamp(): string {
  const now = new Date();
  const pastDate = new Date(now.getTime() - Math.random() * 365 * 24 * 60 * 60 * 1000);
  return pastDate.toISOString().slice(0, 19).replace('T', ' ');
}

/**
 * 生成数据库连接模拟数据
 */
export function generateDBConnectionData(count: number = 1000): DBConnection[] {
  const dbTypes = ['mysql', 'oracle'];
  const timezones = ['Asia/Shanghai', 'Asia/Tokyo', 'UTC', 'America/New_York', 'Europe/London'];
  const connectMethods = ['sid', 'service'];
  const databases = ['main_db', 'user_db', 'order_db', 'product_db', 'log_db', 'analytics_db'];

  return Array.from({ length: count }, (_, index) => {
    const dbType = dbTypes[Math.floor(Math.random() * dbTypes.length)];
    const isMySQL = dbType === 'mysql';

    return {
      id: index + 1,
      name: `数据库连接_${index + 1}_${generateRandomString(4)}`,
      db_type: dbType,
      host: generateRandomIP(),
      port: isMySQL ? '3306' : '1521',
      user: `user_${generateRandomString(6)}`,
      passwd: btoa(`password_${generateRandomString(8)}`), // base64编码
      database: isMySQL ? databases[Math.floor(Math.random() * databases.length)] : '',
      use_ssl: Math.random() > 0.5,
      server_timezone: timezones[Math.floor(Math.random() * timezones.length)],
      instance: !isMySQL ? `ORCL${Math.floor(Math.random() * 99) + 1}` : '',
      connect_method: !isMySQL ? connectMethods[Math.floor(Math.random() * connectMethods.length)] : '',
      create_time: generateRandomTimestamp(),
      update_time: generateRandomTimestamp(),
    };
  });
}

/**
 * 生成告警发送模拟数据
 */
export function generateAlertSendData(count: number = 1000): AlertSend[] {
  const sendTypes = ['kafka', 'prometheus'];
  const topics = ['alert_notifications', 'system_alerts', 'error_logs', 'performance_metrics', 'security_events'];

  return Array.from({ length: count }, (_, index) => {
    const type = sendTypes[Math.floor(Math.random() * sendTypes.length)];
    const isKafka = type === 'kafka';

    return {
      id: index + 1,
      name: `${isKafka ? 'Kafka' : 'Prometheus'}告警发送_${index + 1}_${generateRandomString(4)}`,
      type,
      kafka_receiver: {
        address: isKafka ? `${generateRandomIP()}:9092,${generateRandomIP()}:9092` : '',
        username: isKafka ? `kafka_user_${generateRandomString(6)}` : '',
        password: isKafka ? `kafka_pass_${generateRandomString(8)}` : '',
        topic: isKafka ? topics[Math.floor(Math.random() * topics.length)] : '',
      },
      prometheus_receiver: {
        address: !isKafka ? `${generateRandomIP()}:9090,${generateRandomIP()}:9090` : '',
        username: !isKafka ? `prom_user_${generateRandomString(6)}` : '',
        password: !isKafka ? `prom_pass_${generateRandomString(8)}` : '',
      },
      create_time: generateRandomTimestamp(),
      update_time: generateRandomTimestamp(),
    };
  });
}

/**
 * 生成其他信息模拟数据
 */
export function generateOtherInfoData(count: number = 1000): OtherInfo[] {
  const businessSystems = [
    {
      name: '用户管理系统',
      en: 'user-management',
    },
    {
      name: '订单管理系统',
      en: 'order-management',
    },
    {
      name: '商品管理系统',
      en: 'product-management',
    },
    {
      name: '库存管理系统',
      en: 'inventory-management',
    },
    {
      name: '支付系统',
      en: 'payment-system',
    },
    {
      name: '物流系统',
      en: 'logistics-system',
    },
    {
      name: '客服系统',
      en: 'customer-service',
    },
    {
      name: '数据分析系统',
      en: 'data-analytics',
    },
    {
      name: '监控系统',
      en: 'monitoring-system',
    },
    {
      name: '日志系统',
      en: 'logging-system',
    },
  ];

  const locations = [
    '北京机房',
    '上海机房',
    '广州机房',
    '深圳机房',
    '杭州机房',
    '成都机房',
    '武汉机房',
    '西安机房',
    '南京机房',
    '青岛机房',
    '阿里云华东1',
    '阿里云华北2',
    '腾讯云上海',
    '腾讯云北京',
    'AWS东京',
    'AWS新加坡',
    'Azure香港',
    'Azure首尔',
  ];

  return Array.from({ length: count }, (_, index) => {
    const business = businessSystems[Math.floor(Math.random() * businessSystems.length)];
    const location = locations[Math.floor(Math.random() * locations.length)];

    return {
      id: index + 1,
      name: `${business.name}_${index + 1}_${generateRandomString(4)}`,
      business: business.name,
      business_en: business.en,
      hostname: `${business.en}-server-${String(index + 1).padStart(3, '0')}`,
      location,
      create_time: generateRandomTimestamp(),
      update_time: generateRandomTimestamp(),
    };
  });
}

/**
 * 生成所有类型的模拟数据
 */
export function generateAllMockData(count: number = 1000) {
  return {
    dbConnections: generateDBConnectionData(count),
    alertSends: generateAlertSendData(count),
    otherInfos: generateOtherInfoData(count),
  };
}

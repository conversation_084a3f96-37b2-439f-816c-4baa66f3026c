import { httpClient } from '@/services/http/client';
import type { ApiResponse, OtherInfo, OtherInfoSearchParams } from '../types';

export class OtherInfoService {
  static async addOtherInfo(data: OtherInfo): Promise<ApiResponse<OtherInfo>> {
    console.log('添加其他信息配置:', data);

    const res = httpClient.post<ApiResponse<OtherInfo>>('/api/v1/other/info/add', data);

    return res;
  }

  static async getOtherInfos(params: Partial<OtherInfoSearchParams>): Promise<ApiResponse<OtherInfo>> {
    console.log('获取其他信息列表参数:', params);

    const res = await httpClient.post<ApiResponse<OtherInfo>>('/api/v1/other/info/select', params);

    return res;
  }

  static async deleteOtherInfo(id: number): Promise<ApiResponse<OtherInfo>> {
    console.log('删除其他信息配置:', id);

    const params = {
      ids: [id],
    };

    const res = httpClient.post<ApiResponse<OtherInfo>>('/api/v1/other/info/delete/id', params);

    return res;
  }

  static async batchDeleteOtherInfos(ids: number[]): Promise<ApiResponse<OtherInfo>> {
    console.log('批量删除其他信息配置:', ids);

    const params = {
      ids: ids,
    };

    const res = httpClient.post<ApiResponse<OtherInfo>>('/api/v1/other/info/delete/id', params);

    return res;
  }

  static async updateOtherInfo(data: OtherInfo): Promise<ApiResponse<OtherInfo>> {
    console.log('更新其他信息配置:', data);

    const res = httpClient.post<ApiResponse<OtherInfo>>('/api/v1/other/info/update', data);

    return res;
  }

}
